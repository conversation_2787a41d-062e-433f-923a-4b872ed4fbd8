# This is a basic workflow to help you get started with Actions

name: sync code to gitee

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
# on:
#   push:
#     branches: [ master ]
#   pull_request:
#     branches: [ master ]
on: 
  # Triggers the workflow on push or pull request events but only for the main branch
  push:
    branches: [ main ]
  # pull_request:
  #   branches: [ main ]


# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  sync_gitee:
    name: sync repo to gitee
    # Only run job for specific repository
    if: github.repository == 'sipeed/MaixPy'
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - name: checkout code from github
        uses: actions/checkout@v2

      # Runs a set of commands using the runners shell
      - name: sync shell cmd
        run: |
          GITEE_GIT_ADDR="*************:Sipeed/MaixPy.git"
          git fetch --unshallow
          SSHPATH="$HOME/.ssh"
          rm -rf "$SSHPATH"
          mkdir -p "$SSHPATH"
          echo "${{ secrets.GITEE_SYNC_ACCESSS_KEY }}" > "$SSHPATH/id_rsa"
          chmod 600 "$SSHPATH/id_rsa"
          sudo sh -c "echo StrictHostKeyChecking no >>/etc/ssh/ssh_config"
          git remote add upstream $GITEE_GIT_ADDR
          git push upstream main:main --force
