---

title: Maix<PERSON>y FAQ (Frequently Asked Questions)

---

This page lists common questions and solutions related to MaixPy. If you encounter any issues, please look for answers here first. If you cannot find the answer on this page, you can post detailed steps of your problem in the [MaixHub Discussion Forum](https://maixhub.com/discussion).

For MaixPy-related questions, refer to [MaixPy FAQ](https://wiki.sipeed.com/maixpy/doc/en/faq.html). If you are using MaixCAM, you can also refer to the [MaixCAM FAQ](https://wiki.sipeed.com/hardware/zh/maixcam/faq.html).

## Installation

* Download the precompiled `xxx.whl` file.
* Upload the file to your device, for example, to the `/root` directory.
* Execute the following Python code.
```python
import os

os.system("pip install /root/xxx.whl")
```

