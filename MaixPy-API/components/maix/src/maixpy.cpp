/**
 * All Python API code will auto generated from CPP header, so we don't need to write it manually.
 */

// #include <pybind11/pybind11.h>
// #include "maix_example.h"

// namespace py = pybind11;

// int add(int i, int j) {
//     return i + j;
// }

// PYBIND11_MODULE(_maix, m) {
//     m.doc() = "maix cpp module";

//     m.def("add", &add, "A function that adds two numbers");

//     auto m_a = m.def_submodule("module_a", "This is A.");
//     m_a.def("func", &module_a::func);
// }
