---
title: MaixPy FP5510 使用说明
update:
  - date: 2024-12-02
    author: lxowalle
    version: 1.0.0
    content: 初版文档
---

## FP5510 简介

FP5510是一款单10位DAC,具有120mA输出的电流音圈电机,专为自动对焦操作设计,常用于相机,手机等需要对焦的电子设备.

## MaixPy 中使用 FP5510

MaixPy支持使用`FP5510`对象来操作`fp5510`

示例代码:

```python
from maix.ext_dev import fp5510

fp = fp5510.FP5510()

fp.set_pos(value)
position = fp.get_pos()
print(f'set position to {position}')

```
- 使用`fp5510.FP5510()`方法构造一个操作`fp5510`的对象.一般情况, fp5510可能有`0x0e`和`0x0c`两种从机地址, 通过`slave_addr`参数指定从机地址, 例如:

  > 注意: 如果发现fp5510的地址在`0x0e`和`0x0c`间变化,可能是因为`fp5510`与`摄像头`共用了reset引脚,当使能reset脚时fp5510地址是`0x0c`, 当失能reset脚时fp5510地址是`0x0e`

  ```python
  fp = fp5510.FP5510(slave_addr = 0x0c)
  ```
- 使用`FP5510`类的`set_pos`方法来设置音圈电机的位置,范围为[0, 1023], 例如:

  ```python
  fp.set_pos(500)
  ```
- 使用`FP5510`类的`get_pos`方法来获取音圈电机的位置, 例如:

  ```python
  position = fp.get_pos()
  print(f'set position to {position}')
  ```