from maix import uart, app, time
import sys

def main():
    """串口测试主程序"""
    print("=== MaixPy 串口测试程序 ===")
    print("功能：")
    print("1. 测试串口发送功能")
    print("2. 测试串口接收功能")
    print("3. 回环测试（发送并接收）")
    print("4. 模拟三角形识别数据包发送")
    print("按 Ctrl+C 退出程序")
    print("=" * 30)
    
    # 初始化串口
    try:
        serial = uart.UART("/dev/ttyS0", 115200)
        print("✅ 串口初始化成功: /dev/ttyS0, 115200")
    except Exception as e:
        print(f"❌ 串口初始化失败: {e}")
        return
    
    # 测试计数器
    send_count = 0
    receive_count = 0
    
    print("\n开始串口测试...")
    print("发送测试数据，等待接收...")
    
    while not app.need_exit():
        try:
            # 1. 发送测试数据
            test_send_data(serial, send_count)
            send_count += 1
            
            # 2. 检查接收数据
            received = test_receive_data(serial)
            if received:
                receive_count += 1
            
            # 3. 每10次发送一次模拟数据包
            if send_count % 10 == 0:
                test_triangle_data_packet(serial, send_count)
            
            # 4. 显示统计信息
            if send_count % 20 == 0:
                print(f"\n📊 统计: 发送 {send_count} 次, 接收 {receive_count} 次")
            
            time.sleep_ms(1000)  # 1秒间隔
            
        except KeyboardInterrupt:
            print("\n用户中断程序")
            break
        except Exception as e:
            print(f"❌ 运行错误: {e}")
            time.sleep_ms(100)
    
    print(f"\n📈 最终统计:")
    print(f"总发送次数: {send_count}")
    print(f"总接收次数: {receive_count}")
    print("程序结束")

def test_send_data(serial, count):
    """测试发送数据"""
    # 发送不同类型的测试数据
    test_messages = [
        f"Hello {count}",
        f"Test-{count:04d}",
        "00",  # 启动指令
        f"Count:{count}",
        "PING"
    ]
    
    message = test_messages[count % len(test_messages)]
    
    try:
        # 发送字符串
        serial.write_str(message + "\r\n")
        print(f"📤 发送: {message}")
        
        # 每5次发送一次二进制数据
        if count % 5 == 0:
            binary_data = bytes([0x78, count & 0xFF, (count >> 8) & 0xFF, 0xFC])
            serial.write(binary_data)
            print(f"📤 发送二进制: {binary_data.hex().upper()}")
            
    except Exception as e:
        print(f"❌ 发送失败: {e}")

def test_receive_data(serial):
    """测试接收数据"""
    try:
        # 非阻塞读取
        received_data = serial.read(len=-1, timeout=0)
        if received_data:
            print(f"📥 接收到数据: {received_data}")
            
            # 尝试解析为字符串
            try:
                text = received_data.decode('utf-8').strip()
                print(f"📥 解析为文本: '{text}'")
            except:
                print(f"📥 二进制数据: {received_data.hex().upper()}")
            
            return True
    except Exception as e:
        print(f"❌ 接收失败: {e}")
    
    return False

def test_triangle_data_packet(serial, count):
    """测试三角形识别数据包格式"""
    # 模拟目标点和激光点坐标
    target_x = 160 + (count % 100)  # 模拟目标点移动
    target_y = 120 + (count % 50)
    laser_x = 150 + (count % 80)   # 模拟激光点移动
    laser_y = 110 + (count % 40)
    
    # 构建数据包: 78 target_x_high target_x_low target_y_high target_y_low laser_x_high laser_x_low laser_y_high laser_y_low FC
    data_packet = bytes([
        0x78,                       # 包头
        (target_x >> 8) & 0xFF,     # 目标x高八位
        target_x & 0xFF,            # 目标x低八位
        (target_y >> 8) & 0xFF,     # 目标y高八位
        target_y & 0xFF,            # 目标y低八位
        (laser_x >> 8) & 0xFF,      # 激光x高八位
        laser_x & 0xFF,             # 激光x低八位
        (laser_y >> 8) & 0xFF,      # 激光y高八位
        laser_y & 0xFF,             # 激光y低八位
        0xFC                        # 包尾
    ])
    
    try:
        serial.write(data_packet)
        print(f"🎯 发送三角形数据包: 目标({target_x},{target_y}) 激光({laser_x},{laser_y})")
        print(f"   数据包: {data_packet.hex().upper()}")
    except Exception as e:
        print(f"❌ 数据包发送失败: {e}")

def test_specific_commands():
    """测试特定指令"""
    print("\n=== 特定指令测试 ===")
    
    try:
        serial = uart.UART("/dev/ttyS0", 115200)
        
        # 测试启动指令
        print("发送启动指令 '00'...")
        serial.write_str("00")
        time.sleep_ms(100)
        
        # 测试停止指令
        print("发送停止指令 'FF'...")
        serial.write_str("FF")
        time.sleep_ms(100)
        
        # 测试状态查询
        print("发送状态查询 '?'...")
        serial.write_str("?")
        time.sleep_ms(100)
        
        print("特定指令测试完成")
        
    except Exception as e:
        print(f"❌ 特定指令测试失败: {e}")

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 完整测试 (默认)")
    print("2. 仅发送测试")
    print("3. 仅接收测试")
    print("4. 特定指令测试")
    
    try:
        # 由于MaixPy环境限制，直接运行完整测试
        main()
    except Exception as e:
        print(f"程序异常: {e}")
