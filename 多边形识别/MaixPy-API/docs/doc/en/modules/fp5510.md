---
title: MaixPy FP5510 Instructions
update:
  - date: 2024-12-02
    author: lxowalle
    version: 1.0.0
    content: Initial document
---

## Overview

The **FP5510** is a single 10-bit DAC with a 120mA output current voice coil motor, specifically designed for autofocus operations. It is commonly used in cameras, smartphones, and other electronic devices requiring focus adjustments.

## Using FP5510 in MaixPy

MaixPy supports the operation of the `FP5510` through the `FP5510` object.

Example Code:

```python
from maix.ext_dev import fp5510

fp = fp5510.FP5510()

fp.set_pos(value)
position = fp.get_pos()
print(f'set position to {position}')

```
- Use the `fp5510.FP5510()` method to construct an object for controlling the `fp5510`. Typically, the FP5510 may have slave addresses of `0x0e` or `0x0c`. You can specify the slave address via the `slave_addr` parameter, e.g.:

  > **Note**: If the address of fp5510 is found to change between 0x0e and 0x0c, it may be because the `FP5510` shares a reset pin with the `camera`. When the reset pin is enabled, the FP5510 address is `0x0c`. When the reset pin is disabled, the FP5510 address changes to `0x0e`.
  
  ```python
  fp = fp5510.FP5510(slave_addr = 0x0c)
  ```
- Use the `set_pos` method of the `FP5510` class to set the position of the voice coil motor. The range is [0, 1023]. For example:

  ```python
  fp.set_pos(500)
  ```
- Use the `get_pos` method of the `FP5510` class to get the position of the voice coil motor. For example:

  ```python
  position = fp.get_pos()
  print(f'set position to {position}')
  ```