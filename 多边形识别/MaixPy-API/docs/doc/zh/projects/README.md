---
title: MaixCAM MaixPy 项目实战 介绍和汇总
---

## 简介

这里提供一些常见的项目实战示例，方便社区成员可以参考复现使用，也方便激发大家的灵感做出更多更好的应用和项目出来。

要找到用 MaixPy 实现的相关的项目，有几种方式：

## MaixPy 官方文档

也就是本文档左边目录可以找到的项目实战，比如`小车巡线`。

如果你有好的项目，或者好的项目推荐，也可以贡献文档添加进来。


## MaixHub 项目分享广场


在[MaixHub 项目分享](https://maixhub.com/share?type=project) 栏目可以找到项目分享。

有高质量的分享也会被链接到 MaixPy 官方文档。

你也可以分享你的项目制作方法，会获得官方（必获得）以及社区成员的现金打赏（通常高质量能解决急需需求的更容易被打赏）。


推荐项目：
* maixcam部署yolov5s 自定义模型: https://maixhub.com/share/23


## MaixHub 应用分享

除了项目分享以外，还可以在[MaixHub 应用商店](https://maixhub.com/app) 找到可以直接运行的应用，有部分应用可能是用 MaixPy 编写的，如果作者提供了源码或者写了详细的教程也都可以参考。


推荐项目：

* 简易HTTP串流服务器: https://maixhub.com/app/19
* 桌面电脑性能监视器: https://maixhub.com/app/13
* 安全帽检测模型应用: https://maixhub.com/app/10
