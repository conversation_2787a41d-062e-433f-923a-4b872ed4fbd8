from maix import image, display, app, time, camera, gpio, pinmap, uart
import cv2
import numpy as np
import collections


# ======== 曝光控制参数（可调节） ========
EXPOSURE_MODE = 'auto'  # 曝光模式：'auto' 或 'manual' - 激光检测建议用auto
MANUAL_EXPOSURE_VALUE = 1000  # 手动曝光值 (微秒)，范围通常为 1-1000

# 定义三角形过滤参数
MIN_AREA, MAX_AREA = 2000, 40000  # 最小/最大三角形面积
MIN_RATIO, MAX_RATIO = 0.2, 5  # 最小/最大长宽比
CORNER_THRESH = 8  # 角点位置接近的阈值

# 稳定性参数
HISTORY_LEN = 5  # 历史帧数量
MIN_DETECT_COUNT = 3  # 最小检测次数才认为是有效三角形
STABILITY_THRESH = 20  # 稳定性阈值（像素）

# 三角形调整参数
OUTER_SCALE, INNER_SCALE = 1.0, 0.94  # 外三角形放大比例，内三角形缩小比例

# 历史三角形存储和状态
outer_history = collections.deque(maxlen=HISTORY_LEN)
inner_history = collections.deque(maxlen=HISTORY_LEN)
stable_outer, stable_inner = None, None
final_outer, final_inner = None, None  # 最终稳定的三角形
triangles_detected = False  # 三角形检测完成标志

# 激光路径跟踪参数
TARGET_THRESHOLD = 15  # 激光到达目标点的距离阈值（像素）
path_points = []  # 三角形路径上的所有点（现在是3个边中心点）
current_target_index = 0  # 当前目标点索引
path_generated = False  # 路径是否已生成

def scale_triangle(triangle, scale_factor):
    """按比例缩放三角形"""
    if triangle is None:
        return None
    scaled = triangle.copy()
    M = cv2.moments(triangle)
    if M['m00'] == 0:
        return triangle
    # 计算三角形的中心点
    center = np.array([M['m10'] / M['m00'], M['m01'] / M['m00']])
    # 对每个顶点进行缩放
    for i in range(3):
        vector = np.array(triangle[i][0]) - center  # 从中心到顶点的向量
        new_point = center + vector * scale_factor  # 缩放向量
        scaled[i][0] = new_point.astype(int)  # 更新三角形顶点
    return scaled

def is_triangle(approx):
    """检查轮廓是否为有效三角形"""
    if len(approx) != 3:
        return False
    angles = []
    for i in range(3):
        pt1, pt2, pt3 = approx[i][0], approx[(i+1)%3][0], approx[(i+2)%3][0]
        v1, v2 = pt2 - pt1, pt3 - pt1  # 计算两个向量
        len1, len2 = np.linalg.norm(v1), np.linalg.norm(v2)  # 计算向量的模
        if len1 * len2 == 0:
            return False
        cos_angle = np.clip(np.dot(v1, v2) / (len1 * len2), -1, 1)  # 计算夹角的余弦值
        angle = np.degrees(np.arccos(cos_angle))
        if angle < 15 or angle > 150:  # 检查角度是否在合理范围内
            return False
        angles.append(angle)
    return abs(sum(angles) - 180) <= 15  # 检查三个角的和是否接近180度

def detect_triangles(contours):
    """检测并过滤三角形"""
    triangles = []
    for contour in contours:
        perimeter = cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, 0.04 * perimeter, True)
        if len(approx) == 3 and is_triangle(approx):
            area = cv2.contourArea(approx)  # 计算三角形面积
            x, y, w, h = cv2.boundingRect(approx)  # 计算边界框
            ratio = float(w) / h if h != 0 else 0  # 计算长宽比
            # 过滤不符合条件的三角形
            if MIN_AREA < area < MAX_AREA and MIN_RATIO < ratio < MAX_RATIO:
                triangles.append(approx)
    return triangles

def merge_triangles(triangles):
    """根据角点位置合并接近的三角形"""
    merged = []
    for tri in triangles:
        found = False
        for i, merged_tri in enumerate(merged):
            # 计算角点匹配数量
            matches = sum(1 for c1 in tri for c2 in merged_tri 
                         if np.linalg.norm(c1[0] - c2[0]) < CORNER_THRESH)
            if matches >= 2:  # 三角形只需要2个角点匹配
                if cv2.contourArea(tri) > cv2.contourArea(merged_tri):
                    merged[i] = tri  # 保留面积更大的三角形
                found = True
                break
        if not found:
            merged.append(tri)
    # 按面积排序，只保留最大的两个三角形
    return sorted(merged, key=cv2.contourArea, reverse=True)[:2]

def update_stable_triangle(history, stable_tri, current_tri):
    """更新稳定的三角形，进行平滑处理"""
    if stable_tri is None:
        return current_tri
    # 检查每个角点的稳定性
    for i in range(3):
        if np.linalg.norm(current_tri[i][0] - stable_tri[i][0]) > STABILITY_THRESH:
            return stable_tri  # 如果不稳定，保持原有三角形
    # 平滑更新：80%的旧值 + 20%的新值
    for i in range(3):
        stable_tri[i][0] = (0.8 * stable_tri[i][0] + 0.2 * current_tri[i][0]).astype(int)
    return stable_tri

def detect_red_laser(img):
    """检测红色激光点，只返回坐标不绘制"""
    # 转换颜色空间为 HSV
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    
    # 定义红色激光点颜色范围
    lower_red1 = np.array([160, 82, 40])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 100, 50])
    upper_red2 = np.array([180, 255, 255])
    
    # 创建红色激光的二值化图像
    mask_red1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask_red2 = cv2.inRange(hsv, lower_red2, upper_red2)
    mask_red = cv2.bitwise_or(mask_red1, mask_red2)
    
    # 闭运算
    kernel = np.ones((5, 5), np.uint8)
    mask_red = cv2.morphologyEx(mask_red, cv2.MORPH_CLOSE, kernel)
    
    # 寻找红色激光外轮廓
    contours_red, _ = cv2.findContours(mask_red, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    red_laser_coords = None
    
    print(f"找到 {len(contours_red)} 个红色轮廓")  # 调试信息
    
    for contour in contours_red:
        # 找到最小矩形框
        rect = cv2.minAreaRect(contour)
        # 直接从元组中提取中心坐标
        laser_coords = tuple(map(int, rect[0]))
        r_sum, g_sum = get_pixel_sum(img, laser_coords)
        print(f"候选点 {laser_coords}: R={r_sum}, G={g_sum}")  # 调试信息
        if r_sum > g_sum:
            red_laser_coords = laser_coords
            break  # 只检测第一个红色激光点
    
    return red_laser_coords

def get_pixel_sum(image, coords):
    """获取指定坐标周围区域的R和G通道像素值总和"""
    # 获取图像宽度和高度
    height, width = image.shape[:2]
    radius = 3
    # 确定方圆的左上角和右下角坐标
    x, y = coords
    x_start = max(0, x - radius)
    y_start = max(0, y - radius)
    x_end = min(width - 1, x + radius)
    y_end = min(height - 1, y + radius)
    
    # 提取方圆区域
    roi = image[y_start:y_end, x_start:x_end]
    
    # 计算 R 和 G 通道总值
    r_channel = roi[:, :, 2]  # 红色通道
    g_channel = roi[:, :, 1]  # 绿色通道
    r_sum = int(r_channel.sum())
    g_sum = int(g_channel.sum())
    
    return r_sum, g_sum



def generate_path_between_triangles(outer_triangle, inner_triangle):
    """在外框和内框之间生成激光路径点"""
    if outer_triangle is None or inner_triangle is None:
        return []
    
    path_points = []
    outer_vertices = [outer_triangle[i][0] for i in range(3)]  # 外框顶点
    inner_vertices = [inner_triangle[i][0] for i in range(3)]  # 内框顶点
    
    points_per_edge = 50  # 每条边生成50个点
    
    # 为每条边在外框和内框之间生成路径点
    for i in range(3):
        outer_start = outer_vertices[i]
        outer_end = outer_vertices[(i + 1) % 3]
        inner_start = inner_vertices[i]
        inner_end = inner_vertices[(i + 1) % 3]
        
        # 在每条边上生成多个点
        for j in range(points_per_edge):
            t = j / points_per_edge  # 沿边的插值参数
            
            # 外框边上的点
            outer_point_x = int(outer_start[0] + t * (outer_end[0] - outer_start[0]))
            outer_point_y = int(outer_start[1] + t * (outer_end[1] - outer_start[1]))
            
            # 内框边上的点
            inner_point_x = int(inner_start[0] + t * (inner_end[0] - inner_start[0]))
            inner_point_y = int(inner_start[1] + t * (inner_end[1] - inner_start[1]))
            
            # 外框和内框对应点的中点（激光路径点）
            mid_x = int((outer_point_x + inner_point_x) / 2)
            mid_y = int((outer_point_y + inner_point_y) / 2)
            path_points.append((mid_x, mid_y))
    
    print(f"在外框和内框之间生成了 {len(path_points)} 个路径点")
    return path_points



def update_laser_target(laser_coords, path_points, current_index):
    """更新激光目标点，检查是否到达当前目标"""
    if not path_points or laser_coords is None:
        return current_index
    
    current_target = path_points[current_index]
    distance = np.linalg.norm(np.array(laser_coords) - np.array(current_target))
    
    # 如果激光到达目标点，切换到下一个目标
    if distance <= TARGET_THRESHOLD:
        next_index = (current_index + 1) % len(path_points)
        print(f"激光到达目标点 {current_target}，切换到下一个目标点 {path_points[next_index]}")
        return next_index
    
    return current_index

# 初始化串口
serial = uart.UART("/dev/ttyS0", 115200)
print("串口初始化成功")

def send_position_data(laser_pos, target_pos):
    """发送位置数据包：78 x_high x_low y_high y_low target_x_high target_x_low target_y_high target_y_low fc"""
    # 默认值（如果位置为None）
    laser_x = laser_pos[0] if laser_pos else 0
    laser_y = laser_pos[1] if laser_pos else 0
    target_x = target_pos[0] if target_pos else 0
    target_y = target_pos[1] if target_pos else 0

    # 构建数据包 (10字节)
    data_list = [
        0x78,                   # 包头
        (laser_x >> 8) & 0xFF,  # 激光x高八位
        laser_x & 0xFF,         # 激光x低八位
        (laser_y >> 8) & 0xFF,  # 激光y高八位
        laser_y & 0xFF,         # 激光y低八位
        (target_x >> 8) & 0xFF, # 目标x高八位
        target_x & 0xFF,        # 目标x低八位
        (target_y >> 8) & 0xFF, # 目标y高八位
        target_y & 0xFF,        # 目标y低八位
        0xFC                    # 包尾
    ]

    # 转换为bytes类型
    data_packet = bytes(data_list)

    serial.write(data_packet)
    print(f"发送数据包: 激光({laser_x},{laser_y}) 目标({target_x},{target_y})")

# 初始化相机和显示
disp = display.Display()
cam = camera.Camera(320, 240, image.Format.FMT_BGR888)

# 初始化闪光灯（无论什么曝光模式都开启）
pinmap.set_pin_function("B3", "GPIOB3")
flash_led = gpio.GPIO("GPIOB3", gpio.Mode.OUT)
# 开启闪光灯并保持常亮
flash_led.value(1)
print("闪光灯已开启")

# 设置曝光参数
if EXPOSURE_MODE == 'manual':
    cam.exposure(MANUAL_EXPOSURE_VALUE)
    cam.gain(10000)  # 设置增益值，需在手动曝光模式下使用
else:
    cam.exp_mode(0)

while not app.need_exit():
    img = cam.read()
    if img is None:
        time.sleep(0.1)
        continue
    
    # 转换为OpenCV格式并进行图像预处理
    img = image.image2cv(img, ensure_bgr=False, copy=False)
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)  # 转换为灰度图像
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)  # 高斯平滑滤波
    edges = cv2.Canny(blurred, 50, 150)  # 边缘检测
    dilated = cv2.dilate(edges, np.ones((3, 3), np.uint8), iterations=1)  # 膨胀操作
    
    # 只有在三角形未检测完成时才进行三角形检测
    if not triangles_detected:
        contours, _ = cv2.findContours(dilated, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        triangles = detect_triangles(contours)
        merged_triangles = merge_triangles(triangles)
        
        # 处理检测到的三角形
        if len(merged_triangles) == 2:
            # 确定内外三角形
            outer_tri, inner_tri = (merged_triangles[0], merged_triangles[1]) if \
                cv2.contourArea(merged_triangles[0]) > cv2.contourArea(merged_triangles[1]) else \
                (merged_triangles[1], merged_triangles[0])
            outer_history.append(outer_tri)
            inner_history.append(inner_tri)
        elif len(merged_triangles) == 1:
            tri = merged_triangles[0]
            # 根据面积判断是内三角形还是外三角形
            if cv2.contourArea(tri) > (MAX_AREA + MIN_AREA) / 2:
                outer_history.append(tri)
            else:
                inner_history.append(tri)
        
        # 检查三角形的稳定性并更新
        if len(outer_history) >= MIN_DETECT_COUNT:
            stable_outer = update_stable_triangle(outer_history, stable_outer, outer_history[-1])
        
        if len(inner_history) >= MIN_DETECT_COUNT:
            stable_inner = update_stable_triangle(inner_history, stable_inner, inner_history[-1])
        
        # 检查是否两个三角形都已稳定
        if stable_outer is not None and stable_inner is not None:
            triangles_detected = True
            # 保存最终稳定的三角形状态
            final_outer = scale_triangle(stable_outer, OUTER_SCALE)
            final_inner = scale_triangle(stable_inner, INNER_SCALE)
            print("三角形检测完成，停止继续检测和更新")
            
            # 关闭闪光灯
            flash_led.value(0)
            print("三角形稳定，关闭闪光灯")
    
    # 绘制最终稳定的三角形（不再更新）
    if triangles_detected:
        if final_outer is not None:
            cv2.drawContours(img, [final_outer], -1, (0, 0, 255), 2)  # 外框红色
        if final_inner is not None:
            cv2.drawContours(img, [final_inner], -1, (0, 255, 0), 2)  # 内框绿色
        
        # 生成激光路径（只生成一次）
        if not path_generated and stable_outer is not None and stable_inner is not None:
            # 使用外框和内框生成中点路径
            path_points = generate_path_between_triangles(stable_outer, stable_inner)
            path_generated = True
            print(f"基于外框和内框中点生成激光路径，共 {len(path_points)} 个点")
            
            # 输出实际顶点信息
            outer_vertices = [stable_outer[i][0] for i in range(3)]
            inner_vertices = [stable_inner[i][0] for i in range(3)]
            print(f"外框顶点: {outer_vertices}")
            print(f"内框顶点: {inner_vertices}")
    
    # 检测红色激光点
    red_laser_coords = detect_red_laser(img)
    
    # 激光路径跟踪
    if path_generated and path_points:
        # 更新激光目标点
        if red_laser_coords is not None:
            current_target_index = update_laser_target(red_laser_coords, path_points, current_target_index)
        
        # 绘制当前目标点
        current_target = path_points[current_target_index]
        cv2.circle(img, current_target, 8, (0, 255, 255), 2)  # 青色圆圈表示当前目标点
        cv2.putText(img, f"Target {current_target_index+1}", 
                   (current_target[0] + 10, current_target[1] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
        
        # 绘制激光路径（每10个点显示一个，避免太密集）
        for i, point in enumerate(path_points):
            if i % 10 == 0:  # 每10个点显示一个
                color = (255, 255, 0) if i == current_target_index else (128, 128, 128)
                cv2.circle(img, point, 2, color, -1)
        
        # 绘制下几个目标点的预览
        for i in range(1, min(6, len(path_points))):  # 显示接下来5个目标点
            next_index = (current_target_index + i) % len(path_points)
            next_target = path_points[next_index]
            cv2.circle(img, next_target, 4, (255, 255, 0), 1)  # 黄色圆圈表示下一个目标点
    
    # 绘制红色激光点（在三角形之后绘制，确保不被覆盖）
    if red_laser_coords is not None:
        cv2.circle(img, red_laser_coords, 5, (0, 0, 255), -1)
        print(f"检测到红色激光点: {red_laser_coords}")
        
        # 显示激光到目标点的距离
        if path_generated and path_points:
            current_target = path_points[current_target_index]
            distance = np.linalg.norm(np.array(red_laser_coords) - np.array(current_target))
            cv2.putText(img, f"Distance: {distance:.1f}", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    else:
        print("未检测到红色激光点")
    
    # 发送串口数据包
    if path_generated and path_points:
        current_target = path_points[current_target_index]
        send_position_data(red_laser_coords, current_target)
    
    # 显示结果
    disp.show(image.cv2image(img, bgr=True, copy=False))