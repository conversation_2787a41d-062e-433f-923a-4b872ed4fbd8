from maix import pwm, pinmap, app, time

class ServoGimbal:
    def __init__(self):
        pinmap.set_pin_function("A19", "PWM7")
        pinmap.set_pin_function("A18", "PWM6")
        self.yaw = pwm.PWM(7, freq=50, duty=7.5, enable=True)
        self.pitch = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        
    def set_angle(self, yaw, pitch):
        self.yaw.duty(2.5 + 10 * max(0, min(180, yaw)) / 180)
        self.pitch.duty(2.5 + 10 * max(0, min(180, pitch)) / 180)
        
    def center(self):
        self.yaw.duty(7.5)
        self.pitch.duty(7.5)

if __name__ == "__main__":
    gimbal = ServoGimbal()
    positions = [(90, 90), (45, 90), (135, 90), (90, 45), (90, 135)]
    i = 0
    last = time.time_ms()
    
    while not app.need_exit():
        if time.time_ms() - last > 1000:
            gimbal.set_angle(90,90)
        #     i = (i + 1) % len(positions)
        #     last = time.time_ms()
        # time.sleep_ms(10)