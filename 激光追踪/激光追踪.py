#!/usr/bin/env python3
"""
激光追踪精简版
流程: 点位识别 -> 两点做差 -> x,y差值输入PID -> 输出脉宽 -> 舵机做差输出
"""

from maix import camera, display, app, time, pwm, pinmap, image
import cv2
import numpy as np

# 舵机配置
X_PWM_ID = 6
Y_PWM_ID = 7
SERVO_FREQ = 50
MIN_DUTY = 2.5
MAX_DUTY = 12.5
CENTER_DUTY = 7.5

# 方向控制
X_AXIS_REVERSE = True
Y_AXIS_REVERSE = True

# PID参数

KP = 0.005
KI = 0
KD = 0.00

# 曝光控制参数
EXPOSURE_AUTO = False        # 自动曝光开关
EXPOSURE_VALUE = 100        # 手动曝光值 (1-1000)
GAIN_VALUE = 0              # 增益值 (0-100)
WHITE_BALANCE_AUTO = False   # 自动白平衡

# 激光检测参数
LOWER_RED1 = np.array([0, 100, 50])
UPPER_RED1 = np.array([10, 255, 255])
LOWER_RED2 = np.array([160, 100, 50])
UPPER_RED2 = np.array([180, 255, 255])
LOWER_GREEN = np.array([40, 100, 50])
UPPER_GREEN = np.array([80, 255, 255])


class SimplePID:
    """简单PID控制器"""
    def __init__(self, kp, ki, kd, output_limit=1.0):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.output_limit = output_limit
        self.integral = 0.0
        self.last_error = 0.0
        
    def update(self, error):
        self.integral += error
        derivative = error - self.last_error
        output = self.kp * error + self.ki * self.integral + self.kd * derivative
        
        # 限制输出
        if output > self.output_limit:
            output = self.output_limit
        elif output < -self.output_limit:
            output = -self.output_limit
            
        self.last_error = error
        return output
    
    def reset(self):
        self.integral = 0.0
        self.last_error = 0.0

class LaserTracker:
    """激光追踪器"""
    def __init__(self):
        # 初始化摄像头
        self.cam = camera.Camera(320, 240, image.Format.FMT_BGR888)

        # 直接设置摄像头曝光参数
        if EXPOSURE_AUTO:
            self.cam.exp_mode(0)  # 自动曝光
        else:
            self.cam.exposure(EXPOSURE_VALUE)  # 手动曝光

        # 设置增益
        self.cam.gain(GAIN_VALUE)

        # 设置白平衡
        if WHITE_BALANCE_AUTO:
            self.cam.awb_mode(0)  # 开启自动白平衡
        else:
            self.cam.awb_mode(1)  # 关闭自动白平衡

        # 初始化舵机
        self._init_servos()

        # 初始化PID控制器 - 向量式PID只需要一个控制器
        self.pid = SimplePID(KP, KI, KD)

        # 当前舵机脉宽
        self.current_x_duty = CENTER_DUTY
        self.current_y_duty = CENTER_DUTY

        # 形态学核
        self.kernel = np.ones((5, 5), np.uint8)

    def _init_servos(self):
        """初始化舵机"""
        # 配置引脚
        pinmap.set_pin_function("A18", f"PWM{X_PWM_ID}")
        pinmap.set_pin_function("A19", f"PWM{Y_PWM_ID}")
        
        # 初始化PWM
        self.servo_x = pwm.PWM(X_PWM_ID, freq=SERVO_FREQ, duty=CENTER_DUTY, enable=True)
        self.servo_y = pwm.PWM(Y_PWM_ID, freq=SERVO_FREQ, duty=CENTER_DUTY, enable=True)
    
    def _constrain_duty(self, duty):
        """限制脉宽范围"""
        return max(MIN_DUTY, min(MAX_DUTY, duty))
    
    def detect_lasers(self, img):
        """检测激光点位置"""
        red_pos = None
        green_pos = None
        
        # 转换到HSV
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        
        # 红色激光检测
        mask_red1 = cv2.inRange(hsv, LOWER_RED1, UPPER_RED1)
        mask_red2 = cv2.inRange(hsv, LOWER_RED2, UPPER_RED2)
        mask_red = cv2.bitwise_or(mask_red1, mask_red2)
        mask_red = cv2.morphologyEx(mask_red, cv2.MORPH_CLOSE, self.kernel)
        
        contours_red, _ = cv2.findContours(mask_red, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in contours_red:
            rect = cv2.minAreaRect(contour)
            laser_coords = tuple(map(int, rect[0]))
            r_sum, g_sum = self._get_pixel_sum(img, laser_coords)
            if r_sum > g_sum:
                red_pos = laser_coords
                break
        
        # 绿色激光检测
        mask_green = cv2.inRange(hsv, LOWER_GREEN, UPPER_GREEN)
        mask_green = cv2.morphologyEx(mask_green, cv2.MORPH_CLOSE, self.kernel)
        
        contours_green, _ = cv2.findContours(mask_green, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in contours_green:
            rect = cv2.minAreaRect(contour)
            laser_coords = tuple(map(int, rect[0]))
            r_sum, g_sum = self._get_pixel_sum(img, laser_coords)
            if g_sum > r_sum:
                green_pos = laser_coords
                break
        
        return red_pos, green_pos
    
    def _get_pixel_sum(self, image, coords):
        """获取像素区域RGB总值"""
        height, width = image.shape[:2]
        radius = 3
        x, y = coords
        
        x_start = max(0, x - radius)
        y_start = max(0, y - radius)
        x_end = min(width - 1, x + radius)
        y_end = min(height - 1, y + radius)
        
        roi = image[y_start:y_end, x_start:x_end]
        if roi.size == 0:
            return 0, 0
        
        r_sum = int(roi[:, :, 2].sum())
        g_sum = int(roi[:, :, 1].sum())
        return r_sum, g_sum
    
    def control_servos(self, red_pos, green_pos):
        """控制舵机追踪 - 向量式PID控制"""
        if red_pos is None or green_pos is None:
            return

        # 计算误差向量: 目标位置 - 当前位置
        error_x = green_pos[0] - red_pos[0]
        error_y = green_pos[1] - red_pos[1]
        
        # 计算距离和角度
        distance = np.sqrt(error_x**2 + error_y**2)
        angle = np.arctan2(error_y, error_x)
        
        # 向量式PID: 只对距离进行PID控制
        output = self.pid.update(distance)
        
        # 将控制输出分解到x和y方向
        x_output = output * np.cos(angle)
        y_output = output * np.sin(angle)

        # 更新当前脉宽
        self.current_x_duty += x_output
        self.current_y_duty += y_output

        # 限制脉宽范围
        self.current_x_duty = self._constrain_duty(self.current_x_duty)
        self.current_y_duty = self._constrain_duty(self.current_y_duty)

        # 处理方向反转
        output_x_duty = self.current_x_duty
        output_y_duty = self.current_y_duty

        if X_AXIS_REVERSE:
            output_x_duty = MAX_DUTY + MIN_DUTY - self.current_x_duty
        if Y_AXIS_REVERSE:
            output_y_duty = MAX_DUTY + MIN_DUTY - self.current_y_duty

        # 输出到舵机
        self.servo_x.duty(output_x_duty)
        self.servo_y.duty(output_y_duty)
    

    
    def run(self):
        """运行追踪"""
        while not app.need_exit():
            # 读取图像
            img = self.cam.read()
            img = image.image2cv(img, ensure_bgr=False, copy=False)

            # 检测激光点
            red_pos, green_pos = self.detect_lasers(img)

            # 控制舵机(仅当检测到红色和绿色激光时)
            if red_pos and green_pos:
                self.control_servos(red_pos, green_pos)

            time.sleep_ms(50)  # 20Hz

        self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        # 回到中心位置
        self.servo_x.duty(CENTER_DUTY)
        self.servo_y.duty(CENTER_DUTY)
        time.sleep_ms(500)

if __name__ == "__main__":
    tracker = LaserTracker()
    tracker.run()
